import { isEtRegion } from 'environment';

export const processNumberOfDependents = (
  value: string | number | null | undefined,
): number | null => {
  if (isEtRegion) {
    return value !== '' && value !== null && value !== undefined
      ? +value
      : null;
  } else {
    return +(value ?? 0) || 0;
  }
};

export const processNumberOfDependentsForReactHookForm = (
  value: number | null | undefined,
): number | null => {
  return value ?? null;
};
