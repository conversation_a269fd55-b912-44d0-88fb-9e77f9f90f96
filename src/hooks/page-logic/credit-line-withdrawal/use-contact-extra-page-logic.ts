import {
  ELIGIBILITY_STATES,
  FormFieldNames,
  FormTypes,
  GoogleAnalyticsEvents,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { regionPhonePrefix } from 'environment';
import { useGetCreditLineWithdrawalByHash } from 'hooks/use-get-credit-account-withdrawal-by-hash';
import { useGetOccupationCategories } from 'hooks/use-get-occupation-categories';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { useSendConsentLinkToSpouse } from 'hooks/use-send-consent-link-to-spouse';
import { useUpdateUserInfoExtra } from 'hooks/use-update-user-info-extra';
import { useEffectOnce } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatCreditLineWithdrawalToContactExtraPageDataFormat,
  formatEmploymentDateValueToOptionValue,
  getApplicationStatusForMarketing,
  getEmploymentDateOptions,
  getOccupationCategoriesOptions,
  roundNumberUpByTwoDecimals,
} from 'services';
import { processNumberOfDependents } from 'utils/number-of-dependents';

export const useContactExtraPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);

  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    trackGoogleAnalyticsEvent,
  } = useRootContext();

  const [formType, setFormType] = useState(FormTypes.primary);
  const [instructionsSent, setInstructionsSent] = useState(false);
  const [processingContactExtraPage, setProcessingContactExtraPage] =
    useState(false);
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const { logAction } = useLogCreditAccountAction();

  const { updateUserInfoExtra, userInfoExtraUpdateError } =
    useUpdateUserInfoExtra();
  const {
    sendConsentLinkToSpouse,
    sendingConsentLink,
    sendingConsentLinkError,
  } = useSendConsentLinkToSpouse();
  const { getOccupationCategories, occupationCategories } =
    useGetOccupationCategories();

  const {
    getCreditLineWithdrawal,
    creditLineWithdrawal,
    creditLineWithdrawalLoading,
    refetchCreditLineWithdrawal,
  } = useGetCreditLineWithdrawalByHash();

  const {
    ultimateBeneficialOwner,
    futureReducedEarnings,
    planningNewDebts,
    employmentDate,
    numberOfDependents,
    monthlyLivingExpenses,
    expenditureMonthly,
    netIncomeMonthly,
    occupationCategory,
    productId,
    phoneAreaCode,
    eligibilityState,
    applicationUserInfoId,
    overdueDebt,
  } = useMemo(
    () =>
      formatCreditLineWithdrawalToContactExtraPageDataFormat(
        creditLineWithdrawal,
      ),
    [creditLineWithdrawal],
  );

  const employmentDateOptions = getEmploymentDateOptions(t);

  const contactExtraPageFormConfig = {
    defaultValues: {
      [FormFieldNames.netIncomeMonthly]: netIncomeMonthly,
      [FormFieldNames.expenditureMonthly]: expenditureMonthly ?? 0,
      [FormFieldNames.monthlyLivingExpenses]: monthlyLivingExpenses ?? '',
      [FormFieldNames.numberOfDependents]: numberOfDependents ?? '',
      [FormFieldNames.totalExpenses]: roundNumberUpByTwoDecimals(
        Number(monthlyLivingExpenses ?? 0) + Number(expenditureMonthly ?? 0),
      ),
      [FormFieldNames.employmentDate]: formatEmploymentDateValueToOptionValue(
        employmentDate ?? '',
        employmentDateOptions,
      ),
      [FormFieldNames.planningNewDebts]: planningNewDebts ?? 0,
      [FormFieldNames.overdueDebt]: overdueDebt ?? 0,
      [FormFieldNames.futureReducedEarnings]: futureReducedEarnings ?? 0,
      [FormFieldNames.ultimateBeneficialOwner]: ultimateBeneficialOwner ?? true,
      [FormFieldNames.occupationCategory]: occupationCategory
        ? t(
            `${
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryPrefix
            }-${occupationCategory.replaceAll('_', '-').toLowerCase()}`,
          )
        : null,
    },
  };

  const userInfoExtraValidationErrors = extractValidationErrors(
    userInfoExtraUpdateError,
  );

  const sendingConsentLinkValidationErrors = extractValidationErrors(
    sendingConsentLinkError,
  );

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const occupationCategoryOptions = getOccupationCategoriesOptions(
    t,
    occupationCategories,
  );

  const contactExtraPageLoaded = Boolean(
    (visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown]
      ? occupationCategories?.length
      : true) &&
      !pageUrlAndNavigationProcessing &&
      !pageAttributesLoading &&
      !creditLineWithdrawalLoading &&
      productId,
  );

  const onChangeTotalExpenses = (
    formValues: FieldValues,

    formMethods: UseFormReturn<FieldValues, any>,
  ) => {
    const { setValue } = formMethods;
    const { expenditure_monthly, monthly_living_expenses } = formValues;

    setValue(
      FormFieldNames.totalExpenses,
      Number(expenditure_monthly) + Number(monthly_living_expenses) || '',
    );
  };

  const submitPrimaryForm = async (
    {
      number_of_dependents,
      expenditure_monthly,
      monthly_living_expenses,
      net_income_monthly,
      planning_new_debts,
      employment_date,
      future_reduced_earnings,
      ultimate_beneficial_owner,
      occupation_category,
      overdue_debt,
    }: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    setProcessingContactExtraPage(true);

    const variablesFilteredByVisiblePageAttributes =
      filterObjectByExistingKeysInObject(
        {
          number_of_dependents: processNumberOfDependents(number_of_dependents),
          expenditure_monthly:
            expenditure_monthly !== '' ? +expenditure_monthly : null,
          monthly_living_expenses:
            monthly_living_expenses !== '' ? +monthly_living_expenses : null,
          net_income_monthly: +net_income_monthly || netIncomeMonthly || null,
          planning_new_debts: +planning_new_debts || 0,
          employment_date: employment_date || employmentDate || null,
          future_reduced_earnings: +future_reduced_earnings || 0,
          ultimate_beneficial_owner:
            ultimate_beneficial_owner ?? ultimateBeneficialOwner ?? true,
          occupation_category: occupation_category || occupationCategory,
          overdue_debt: +overdue_debt || 0,
        },
        formMethods.control._fields,
      );

    try {
      const appEligibilityState =
        eligibilityState ?? ELIGIBILITY_STATES.pending;
      await updateUserInfoExtra({
        application_user_info_id: applicationUserInfoId ?? 0,
        reject_when_necessary: true,
        extra_income: 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      await refetchCreditLineWithdrawal();

      await getPageUrlAndNavigate(true);
      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactExtraCompleted, {
        status: getApplicationStatusForMarketing(appEligibilityState),
      });
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }
    } finally {
      setProcessingContactExtraPage(false);
    }
  };

  const submitSecondaryForm = async (formFieldValues: FieldValues) => {
    const { spouse_email, spouse_phone } = formFieldValues;

    const response = await sendConsentLinkToSpouse({
      credit_account_id: productId,
      spouse_email,
      spouse_phone,
    });

    if (!response.data?.success) {
      throw new Error('Failed to send consent link to spouse');
    }

    setInstructionsSent(true);
  };

  const onContactExtraFormSubmit = (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    switch (formType) {
      case FormTypes.primary:
        submitPrimaryForm(formFieldValues, formMethods);
        break;
      case FormTypes.secondary:
        submitSecondaryForm(formFieldValues);
        break;
      default:
        break;
    }
  };

  useEffectOnce(() => {
    getPageAttributes();
  });

  useEffectOnce(() => {
    getCreditLineWithdrawal();
  });

  useEffect(() => {
    if (
      visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown] &&
      !occupationCategories
    ) {
      getOccupationCategories();
    }
  }, [visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown]]);

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactExtraInfo,
      });
    }
  }, [productId]);

  return useMemo(
    () => ({
      onContactExtraFormSubmit,
      contactExtraPageFormConfig,
      contactExtraPageLoaded,
      processingContactExtraPage,
      visiblePageAttributes,
      onChangeTotalExpenses,
      userInfoExtraValidationErrors,
      sendingConsentLinkValidationErrors,
      setFormType,
      formType,
      setInstructionsSent,
      sendingConsentLink,
      occupationCategoryOptions,
      employmentDateOptions,
      phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
      instructionsSent,
    }),
    [
      onContactExtraFormSubmit,
      contactExtraPageFormConfig,
      contactExtraPageLoaded,
      processingContactExtraPage,
      visiblePageAttributes,
      onChangeTotalExpenses,
      userInfoExtraValidationErrors,
      sendingConsentLinkValidationErrors,
      formType,
      sendingConsentLink,
      occupationCategoryOptions,
      employmentDateOptions,
      phoneAreaCode,
      instructionsSent,
    ],
  );
};
