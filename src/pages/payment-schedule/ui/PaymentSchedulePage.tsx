import {
  BreadcrumbNavigation,
  type BreadcrumbNavigationItem,
} from '@components/breadcrumb-navigation';
import { Helmet } from '@components/Helmet';
import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS } from '@config/locize/common';
import { ROUTE_NAMES } from '@config/routes';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { useToast } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { getProductByScheduleType } from '@utils/getProductByApplicationScheduleType';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import {
  ApplicationScheduleType,
  InstallmentinstallmentDate,
} from '@/shared/types';

import { paymentSchedulePageApi } from '../api';

const routeApi = getRouteApi(
  '/_protected/_main/agreements/schedule/$applicationId',
);

const MONTHLY_TYPES = [
  InstallmentinstallmentDate.MONTHLY_CONTRACT_FEE,
  InstallmentinstallmentDate.MONTHLY_INTEREST,
  InstallmentinstallmentDate.MONTHLY_MANAGEMENT_FEE,
  InstallmentinstallmentDate.MONTHLY_PRINCIPAL,
];

const DOWN_TYPES = [
  InstallmentinstallmentDate.DOWN_CONTRACT_FEE,
  InstallmentinstallmentDate.DOWN_PRINCIPAL,
];

interface ModifiedInstallment {
  due_at: string;
  amount: number;
  paid: number;
  type: InstallmentinstallmentDate | 'MONTHLY' | 'DOWN';
}

type RawInstallment = {
  id?: number | null;
  paid: number;
  due_at: string;
  amount: number;
  type?: InstallmentinstallmentDate | null;
};

function instType(
  installment: RawInstallment,
): InstallmentinstallmentDate | 'MONTHLY' | 'DOWN' | null {
  if (!installment.type) {
    return null;
  } else if (DOWN_TYPES.includes(installment.type)) {
    return 'DOWN';
  } else if (MONTHLY_TYPES.includes(installment.type)) {
    return 'MONTHLY';
  } else {
    return installment.type;
  }
}

function instKey(installment: RawInstallment): string {
  return installment.due_at + '-' + instType(installment);
}

function sortInstallments(
  a: ModifiedInstallment,
  b: ModifiedInstallment,
): number {
  return new Date(a.due_at).getTime() - new Date(b.due_at).getTime();
}

function mapToModified(inst: RawInstallment): ModifiedInstallment {
  return {
    type: instType(inst) ?? inst.type ?? InstallmentinstallmentDate.CHARGE,
    due_at: inst.due_at,
    amount: inst.amount,
    paid: inst.paid,
  };
}

function groupChargeInstallments(
  charges: ModifiedInstallment[],
): ModifiedInstallment[] {
  if (charges.length === 0) {
    return [];
  }

  const singleCharge = charges.slice(1, charges.length).reduce(
    (acc, inst) => ({
      ...acc,
      due_at: inst.due_at,
      amount: acc.amount + inst.amount,
      paid: acc.paid + inst.paid,
    }),
    charges[0],
  );

  return [singleCharge];
}

function groupInstallmentsByMonth(
  installments: RawInstallment[],
): ModifiedInstallment[] {
  const instMap: { [key: string]: ModifiedInstallment } = {};

  installments.forEach((inst) => {
    const key = instKey(inst);
    if (!instMap[key]) {
      instMap[key] = {
        type: instType(inst) ?? inst.type ?? InstallmentinstallmentDate.CHARGE,
        due_at: inst.due_at,
        amount: inst.amount,
        paid: inst.paid,
      };
    } else {
      instMap[key].amount += inst.amount;
      instMap[key].paid += inst.paid;
    }
  });

  return Object.keys(instMap).reduce<ModifiedInstallment[]>(
    (acc, key) => acc.concat(instMap[key]),
    [],
  );
}

function groupInstallments(
  installments: RawInstallment[],
): ModifiedInstallment[] {
  const charges = groupChargeInstallments(
    installments
      .filter((inst) => inst.type === InstallmentinstallmentDate.CHARGE)
      .map(mapToModified),
  );
  const others = groupInstallmentsByMonth(
    installments.filter(
      (inst) => inst.type !== InstallmentinstallmentDate.CHARGE,
    ),
  );

  return [...others, ...charges].sort(sortInstallments);
}

export const PaymentSchedulePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);
  const { applicationId } = routeApi.useParams();
  const agreementReferenceKey = applicationId; // applicationId is actually the reference key

  const navigate = routeApi.useNavigate();

  const { data, isSuccess, isError, isFetching } =
    paymentSchedulePageApi.usePaymentScheduleApplicationQuery(
      {
        referenceKey: agreementReferenceKey,
      },
      {
        select: (data) => {
          if (!data?.application_by_reference) return null;

          const application = data.application_by_reference;
          const scheduleType = application.schedule_type;

          const { title, description } =
            AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[scheduleType];

          // Filter and process installments
          const rawInstallments = (application.installments || []).filter(
            (inst): inst is RawInstallment =>
              inst !== null && (inst.amount > 0 || inst.paid > 0),
          );

          // Separate reminders from other installments
          const reminderInstallments = rawInstallments
            .filter((inst) => inst.type === InstallmentinstallmentDate.REMINDER)
            .map(mapToModified)
            .sort(sortInstallments);

          const otherInstallments = groupInstallments(
            rawInstallments.filter(
              (inst) => inst.type !== InstallmentinstallmentDate.REMINDER,
            ),
          );

          return {
            title,
            description,
            scheduleType,
            agreementDescription:
              application.merchant?.name || (description && t(description)),
            productType: getProductByScheduleType(scheduleType),
            installments: {
              reminder: reminderInstallments,
              other: otherInstallments,
            },
            merchant: {
              campaignName:
                application.merchant?.campaign?.converting_schedule_name,
              name: application.merchant?.name,
              logoSrc: application.merchant?.logo_path,
            },
          };
        },
      },
    );

  const { showErrorMessage } = useToast();

  const isInvalidApplication = (isSuccess && !data) || isError;

  const hirePurchaseTypes = [
    ApplicationScheduleType.ESTO_PAY,
    ApplicationScheduleType.ESTO_X,
    ApplicationScheduleType.PAY_LATER,
    ApplicationScheduleType.REGULAR,
  ];
  const isHP = data ? hirePurchaseTypes.includes(data.scheduleType) : false;

  const handleDealsClick = () => {
    navigate({
      to: ROUTE_NAMES.agreements,
    });
  };

  const handleSmallLoanClick = () => {
    navigate({
      to: ROUTE_NAMES.agreements,
      search: {
        agreementReferenceKey,
      },
    });
  };

  const breadcrumbItems: BreadcrumbNavigationItem[] = [
    {
      label: t(LOCIZE_AGREEMENTS_KEYS.pageTitle),
      onClick: handleDealsClick,
      isActive: false,
    },
    {
      label: (isHP ? data?.merchant.name : t(data?.title as string)) ?? '',
      onClick: handleSmallLoanClick,
      isActive: false,
    },
    {
      label: t(LOCIZE_AGREEMENTS_KEYS.modalPaymentSchedule),
      isActive: true,
    },
  ];

  useUpdateEffect(() => {
    if (isInvalidApplication) {
      showErrorMessage(
        isError ? 'Something went wrong' : 'Agreement not found',
      );
    }
  });

  if (isFetching || !data) {
    return <PaymentSchedulePageSkeleton />;
  }

  if (isInvalidApplication) {
    navigate({
      to: ROUTE_NAMES.agreements,
      replace: true,
    });
    return null;
  }

  return (
    <>
      <Helmet title="Payment Schedule" />
      <div className="flex h-full w-full flex-col items-center overflow-y-auto">
        <div className="flex w-full max-w-6xl flex-col items-center px-12 py-12 pb-[7.5rem]">
          <div className="w-full pb-10">
            <BreadcrumbNavigation items={breadcrumbItems} />
          </div>

          <div className="w-full pb-10">
            <div className="flex items-center gap-4">
              <ProductIcon
                className="h-[4.5rem] w-[4.5rem]"
                merchantLogoSrc={data.merchant.logoSrc}
                productType={data.productType}
                size="large"
              />
              <div className="flex flex-col gap-2">
                <Typography variant="m" affects="semibold">
                  {data.scheduleType === ApplicationScheduleType.ESTO_X
                    ? data.merchant.campaignName
                    : t(data.title)}
                </Typography>
                {data.agreementDescription ? (
                  <Typography variant="text-l" className="text-gray-500">
                    {data.agreementDescription}
                  </Typography>
                ) : null}
              </div>
            </div>
          </div>

          <div className="w-full space-y-4">
            {data.installments.other.length > 0 && (
              <div className="rounded-2xl border border-neutral-200">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[160px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableDateCell)}
                      </TableHead>
                      <TableHead className="w-[140px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableToPayCell)}
                      </TableHead>
                      <TableHead className="w-[140px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTablePaidCell)}
                      </TableHead>
                      <TableHead className="w-[200px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableTypeCell)}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.installments.other.map((installment, index) => (
                      <TableRow key={`other-${installment.due_at}-${index}`}>
                        <TableCell className="w-[160px]">
                          <Typography variant="text-s">
                            {formatDate(new Date(installment.due_at))}
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[140px]">
                          <Typography variant="text-s">
                            {formatNumber({
                              value: installment.amount,
                              minimumFractionDigits: 2,
                            })}{' '}
                            €
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[140px]">
                          <Typography variant="text-s">
                            {formatNumber({
                              value: installment.paid,
                              minimumFractionDigits: 2,
                            })}{' '}
                            €
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[200px]">
                          <Typography variant="text-s">
                            {installment.type &&
                            LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS[
                              installment.type
                            ]
                              ? t(
                                  LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS[
                                    installment.type
                                  ],
                                )
                              : String(installment.type)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {data.installments.reminder.length > 0 && (
              <div className="rounded-2xl border border-neutral-200">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[160px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableDateCell)}
                      </TableHead>
                      <TableHead className="w-[140px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableToPayCell)}
                      </TableHead>
                      <TableHead className="w-[140px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTablePaidCell)}
                      </TableHead>
                      <TableHead className="w-[200px]">
                        {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableTypeCell)}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.installments.reminder.map((installment, index) => (
                      <TableRow key={`reminder-${installment.due_at}-${index}`}>
                        <TableCell className="w-[160px]">
                          <Typography variant="text-s">
                            {formatDate(new Date(installment.due_at))}
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[140px]">
                          <Typography variant="text-s">
                            {formatNumber({
                              value: installment.amount,
                              minimumFractionDigits: 2,
                            })}{' '}
                            €
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[140px]">
                          <Typography variant="text-s">
                            {formatNumber({
                              value: installment.paid,
                              minimumFractionDigits: 2,
                            })}{' '}
                            €
                          </Typography>
                        </TableCell>
                        <TableCell className="w-[200px]">
                          <Typography variant="text-s">
                            {installment.type &&
                            LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS[
                              installment.type
                            ]
                              ? t(
                                  LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS[
                                    installment.type
                                  ],
                                )
                              : String(installment.type)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

function PaymentSchedulePageSkeleton() {
  return (
    <div className="flex h-full w-full flex-col items-center overflow-y-auto">
      <div className="flex w-full max-w-6xl flex-col items-center px-12 py-12 pb-[7.5rem]">
        <div className="w-full pb-10">
          <Skeleton className="h-6 w-64" />
        </div>

        <div className="w-full pb-10">
          <div className="flex items-center gap-4">
            <Skeleton className="h-18 w-18 rounded-full" />
            <div className="flex flex-col gap-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-6 w-32" />
            </div>
          </div>
        </div>

        <div className="w-full">
          <div className="rounded-2xl border border-neutral-200">
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    </div>
  );
}
